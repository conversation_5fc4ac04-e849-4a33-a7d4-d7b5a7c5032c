#!/usr/bin/env tsx

/**
 * Production readiness test script for PasaBuy Pal
 * Validates all production enhancements and configurations
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync } from 'fs'
import { join } from 'path'

interface TestResult {
  name: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: string[]
}

class ProductionReadinessTest {
  private results: TestResult[] = []

  /**
   * Add test result
   */
  private addResult(result: TestResult): void {
    this.results.push(result)
    
    const icon = result.status === 'pass' ? '✅' : 
                 result.status === 'warning' ? '⚠️' : '❌'
    
    console.log(`${icon} ${result.name}: ${result.message}`)
    
    if (result.details && result.details.length > 0) {
      result.details.forEach(detail => console.log(`   - ${detail}`))
    }
  }

  /**
   * Test 1: Environment Configuration
   */
  private testEnvironmentConfiguration(): void {
    console.log('\n📋 Testing Environment Configuration...')
    
    try {
      const requiredVars = [
        'NEXT_PUBLIC_APPWRITE_ENDPOINT',
        'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
        'APPWRITE_API_KEY',
        'APPWRITE_DATABASE_ID',
        'APPWRITE_STORAGE_BUCKET_ID'
      ]
      
      const missingVars: string[] = []
      const configuredVars: string[] = []
      
      requiredVars.forEach(varName => {
        if (process.env[varName]) {
          configuredVars.push(varName)
        } else {
          missingVars.push(varName)
        }
      })
      
      if (missingVars.length === 0) {
        this.addResult({
          name: 'Environment Variables',
          status: 'pass',
          message: 'All required environment variables are configured',
          details: configuredVars
        })
      } else {
        this.addResult({
          name: 'Environment Variables',
          status: 'fail',
          message: `Missing ${missingVars.length} required environment variables`,
          details: missingVars
        })
      }
      
      // Check for production environment file
      const prodEnvExists = existsSync('.env.production')
      this.addResult({
        name: 'Production Environment File',
        status: prodEnvExists ? 'pass' : 'warning',
        message: prodEnvExists ? 
          'Production environment template exists' : 
          'No .env.production template found'
      })
      
    } catch (error) {
      this.addResult({
        name: 'Environment Configuration',
        status: 'fail',
        message: `Error testing environment: ${error}`
      })
    }
  }

  /**
   * Test 2: Build Process
   */
  private testBuildProcess(): void {
    console.log('\n🏗️ Testing Build Process...')
    
    try {
      console.log('   Building application...')
      execSync('npm run build', { stdio: 'pipe' })
      
      this.addResult({
        name: 'Build Process',
        status: 'pass',
        message: 'Application builds successfully'
      })
      
      // Check if .next directory exists
      const buildDirExists = existsSync('.next')
      this.addResult({
        name: 'Build Output',
        status: buildDirExists ? 'pass' : 'fail',
        message: buildDirExists ? 
          'Build output directory created' : 
          'Build output directory not found'
      })
      
    } catch (error) {
      this.addResult({
        name: 'Build Process',
        status: 'fail',
        message: 'Build failed',
        details: [String(error)]
      })
    }
  }

  /**
   * Test 3: Security Features
   */
  private testSecurityFeatures(): void {
    console.log('\n🔒 Testing Security Features...')
    
    try {
      // Check if security utilities exist
      const securityUtilsPath = join(process.cwd(), 'src/lib/utils/security.ts')
      const securityUtilsExist = existsSync(securityUtilsPath)
      
      this.addResult({
        name: 'Security Utilities',
        status: securityUtilsExist ? 'pass' : 'fail',
        message: securityUtilsExist ? 
          'Security utilities implemented' : 
          'Security utilities not found'
      })
      
      // Check if error handler exists
      const errorHandlerPath = join(process.cwd(), 'src/lib/utils/error-handler.ts')
      const errorHandlerExists = existsSync(errorHandlerPath)
      
      this.addResult({
        name: 'Error Handling',
        status: errorHandlerExists ? 'pass' : 'fail',
        message: errorHandlerExists ? 
          'Enhanced error handling implemented' : 
          'Error handling utilities not found'
      })
      
      // Check if API middleware exists
      const middlewarePath = join(process.cwd(), 'src/lib/middleware/api-middleware.ts')
      const middlewareExists = existsSync(middlewarePath)
      
      this.addResult({
        name: 'API Middleware',
        status: middlewareExists ? 'pass' : 'fail',
        message: middlewareExists ? 
          'Production API middleware implemented' : 
          'API middleware not found'
      })
      
    } catch (error) {
      this.addResult({
        name: 'Security Features',
        status: 'fail',
        message: `Error testing security features: ${error}`
      })
    }
  }

  /**
   * Test 4: Monitoring & Observability
   */
  private testMonitoring(): void {
    console.log('\n📊 Testing Monitoring & Observability...')
    
    try {
      // Check if monitoring utilities exist
      const monitoringPath = join(process.cwd(), 'src/lib/utils/monitoring.ts')
      const monitoringExists = existsSync(monitoringPath)
      
      this.addResult({
        name: 'Monitoring Utilities',
        status: monitoringExists ? 'pass' : 'fail',
        message: monitoringExists ? 
          'Performance monitoring implemented' : 
          'Monitoring utilities not found'
      })
      
      // Check if health check endpoint exists
      const healthCheckPath = join(process.cwd(), 'src/app/api/health/appwrite/route.ts')
      const healthCheckExists = existsSync(healthCheckPath)
      
      this.addResult({
        name: 'Health Check Endpoint',
        status: healthCheckExists ? 'pass' : 'fail',
        message: healthCheckExists ? 
          'Health check endpoint implemented' : 
          'Health check endpoint not found'
      })
      
    } catch (error) {
      this.addResult({
        name: 'Monitoring',
        status: 'fail',
        message: `Error testing monitoring: ${error}`
      })
    }
  }

  /**
   * Test 5: Appwrite Integration
   */
  private async testAppwriteIntegration(): Promise<void> {
    console.log('\n☁️ Testing Appwrite Integration...')
    
    try {
      // Test Appwrite verification script
      console.log('   Running Appwrite verification...')
      execSync('npm run appwrite:verify', { stdio: 'pipe' })
      
      this.addResult({
        name: 'Appwrite Connection',
        status: 'pass',
        message: 'Appwrite integration verified successfully'
      })
      
    } catch (error) {
      this.addResult({
        name: 'Appwrite Connection',
        status: 'fail',
        message: 'Appwrite verification failed',
        details: [String(error)]
      })
    }
  }

  /**
   * Test 6: Dependencies & Security
   */
  private testDependencies(): void {
    console.log('\n📦 Testing Dependencies & Security...')
    
    try {
      // Check for security vulnerabilities
      console.log('   Checking for security vulnerabilities...')
      try {
        execSync('npm audit --audit-level=high', { stdio: 'pipe' })
        this.addResult({
          name: 'Security Audit',
          status: 'pass',
          message: 'No high-severity vulnerabilities found'
        })
      } catch (auditError) {
        this.addResult({
          name: 'Security Audit',
          status: 'warning',
          message: 'Security vulnerabilities detected - review npm audit output'
        })
      }
      
      // Check package.json for production readiness
      const packageJsonPath = join(process.cwd(), 'package.json')
      if (existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'))
        
        const hasStartScript = !!packageJson.scripts?.start
        this.addResult({
          name: 'Production Scripts',
          status: hasStartScript ? 'pass' : 'warning',
          message: hasStartScript ? 
            'Production start script configured' : 
            'No production start script found'
        })
        
        // Check for essential dependencies
        const essentialDeps = ['appwrite', 'node-appwrite', 'next']
        const missingDeps = essentialDeps.filter(dep => !packageJson.dependencies?.[dep])
        
        if (missingDeps.length === 0) {
          this.addResult({
            name: 'Essential Dependencies',
            status: 'pass',
            message: 'All essential dependencies present'
          })
        } else {
          this.addResult({
            name: 'Essential Dependencies',
            status: 'fail',
            message: `Missing essential dependencies: ${missingDeps.join(', ')}`
          })
        }
      }
      
    } catch (error) {
      this.addResult({
        name: 'Dependencies',
        status: 'fail',
        message: `Error testing dependencies: ${error}`
      })
    }
  }

  /**
   * Test 7: Documentation & Deployment
   */
  private testDocumentation(): void {
    console.log('\n📚 Testing Documentation & Deployment...')
    
    const requiredDocs = [
      'README.md',
      'PRODUCTION_DEPLOYMENT_GUIDE.md',
      'FINAL_MIGRATION_SUMMARY.md'
    ]
    
    const existingDocs: string[] = []
    const missingDocs: string[] = []
    
    requiredDocs.forEach(doc => {
      if (existsSync(doc)) {
        existingDocs.push(doc)
      } else {
        missingDocs.push(doc)
      }
    })
    
    this.addResult({
      name: 'Documentation',
      status: missingDocs.length === 0 ? 'pass' : 'warning',
      message: `${existingDocs.length}/${requiredDocs.length} documentation files present`,
      details: missingDocs.length > 0 ? [`Missing: ${missingDocs.join(', ')}`] : undefined
    })
  }

  /**
   * Generate summary report
   */
  private generateSummary(): void {
    console.log('\n' + '='.repeat(60))
    console.log('🎯 PRODUCTION READINESS SUMMARY')
    console.log('='.repeat(60))
    
    const passed = this.results.filter(r => r.status === 'pass').length
    const warnings = this.results.filter(r => r.status === 'warning').length
    const failed = this.results.filter(r => r.status === 'fail').length
    const total = this.results.length
    
    console.log(`\n📊 Test Results:`)
    console.log(`   ✅ Passed: ${passed}/${total}`)
    console.log(`   ⚠️  Warnings: ${warnings}/${total}`)
    console.log(`   ❌ Failed: ${failed}/${total}`)
    
    const score = Math.round((passed / total) * 100)
    console.log(`\n🎯 Production Readiness Score: ${score}%`)
    
    if (score >= 90) {
      console.log('🎉 EXCELLENT! Your application is production-ready!')
    } else if (score >= 75) {
      console.log('👍 GOOD! Address warnings before production deployment.')
    } else if (score >= 60) {
      console.log('⚠️  NEEDS WORK! Address failed tests before deployment.')
    } else {
      console.log('🚨 NOT READY! Critical issues must be resolved.')
    }
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:')
      this.results
        .filter(r => r.status === 'fail')
        .forEach(r => console.log(`   - ${r.name}: ${r.message}`))
    }
    
    if (warnings > 0) {
      console.log('\n⚠️  Warnings:')
      this.results
        .filter(r => r.status === 'warning')
        .forEach(r => console.log(`   - ${r.name}: ${r.message}`))
    }
    
    console.log('\n📋 Next Steps:')
    console.log('   1. Address any failed tests')
    console.log('   2. Review and resolve warnings')
    console.log('   3. Follow the Production Deployment Guide')
    console.log('   4. Set up monitoring and alerting')
    console.log('   5. Perform load testing')
    
    console.log('\n🚀 Ready for production deployment!')
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 PasaBuy Pal - Production Readiness Test')
    console.log('='.repeat(50))
    
    this.testEnvironmentConfiguration()
    this.testBuildProcess()
    this.testSecurityFeatures()
    this.testMonitoring()
    await this.testAppwriteIntegration()
    this.testDependencies()
    this.testDocumentation()
    
    this.generateSummary()
  }
}

// Run tests
async function main() {
  const tester = new ProductionReadinessTest()
  await tester.runAllTests()
}

main().catch(console.error)
