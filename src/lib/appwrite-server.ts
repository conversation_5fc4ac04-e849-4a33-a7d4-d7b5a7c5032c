import { config } from 'dotenv'
import { Client as NodeClient, Databases as NodeDatabases, Storage as NodeStorage, Users as NodeUsers, Query, Permission, Role, ID } from 'node-appwrite'

// Load environment variables for server-side usage
config()

// Server-side Appwrite configuration
export const serverClient = new NodeClient()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://localhost/v1')
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '6840ec030002516fbdc3')
  .setKey(process.env.APPWRITE_API_KEY || '')

// Server-side services
export const serverDatabases = new NodeDatabases(serverClient)
export const serverStorage = new NodeStorage(serverClient)
export const serverUsers = new NodeUsers(serverClient)

// Configuration constants
export const DB_ID = process.env.APPWRITE_DATABASE_ID || '6840ec030002516fbdc3'; // Using DB_ID as requested, aliasing existing DATABASE_ID logic
export const STORAGE_BUCKET_ID = process.env.APPWRITE_STORAGE_BUCKET_ID || '6840ec8e001ec3f92096';

// Appwrite Collection IDs

// Known existing collections (adjust placeholder names if specific env vars are not used for each)
export const STORE_CODES_COLLECTION_ID = process.env.APPWRITE_STORE_CODES_COLLECTION_ID || 'stores';
export const CUSTOMERS_COLLECTION_ID = process.env.APPWRITE_CUSTOMERS_COLLECTION_ID || 'customers';
export const ORDERS_COLLECTION_ID = process.env.APPWRITE_ORDERS_COLLECTION_ID || 'orders';
export const INVOICES_COLLECTION_ID = process.env.APPWRITE_INVOICES_COLLECTION_ID || 'invoices';
// export const INVOICE_ITEMS_COLLECTION_ID = process.env.APPWRITE_INVOICE_ITEMS_COLLECTION_ID || 'invoice_items'; // Example if needed later

// New collections
export const STORE_PRICING_COLLECTION_ID = process.env.APPWRITE_STORE_PRICING_COLLECTION_ID || 'store_pricing_rules';
export const PRICING_SETTINGS_COLLECTION_ID = process.env.APPWRITE_PRICING_SETTINGS_COLLECTION_ID || 'pricing_settings';
export const FILTER_PRESETS_COLLECTION_ID = process.env.APPWRITE_FILTER_PRESETS_COLLECTION_ID || 'filter_presets';


// Type definitions for Appwrite documents
export interface AppwriteDocument {
  $id: string
  $createdAt: string
  $updatedAt: string
  $permissions: string[]
  $collectionId: string
  $databaseId: string
}

// Helper function to convert Prisma data to Appwrite format
export function convertToAppwriteDocument<T extends Record<string, any>>(
  data: T,
  excludeFields: string[] = ['id', 'createdAt', 'updatedAt']
): Omit<T, typeof excludeFields[number]> {
  const converted = { ...data }
  
  // Remove fields that Appwrite handles automatically
  excludeFields.forEach(field => {
    delete converted[field]
  })
  
  return converted
}

// Helper function to convert Appwrite document to app format
export function convertFromAppwriteDocument<T>(
  doc: AppwriteDocument & Record<string, any>
): T & { id: string; createdAt: string; updatedAt: string } {
  const { $id, $createdAt, $updatedAt, $permissions, $collectionId, $databaseId, ...data } = doc
  
  return {
    ...data,
    id: $id,
    createdAt: $createdAt,
    updatedAt: $updatedAt
  } as T & { id: string; createdAt: string; updatedAt: string }
}

// Export the actual Appwrite Query, Permission, Role, and ID classes
export { Query, Permission, Role, ID }
