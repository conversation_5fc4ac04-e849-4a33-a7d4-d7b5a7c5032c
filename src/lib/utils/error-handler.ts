/**
 * Production-ready error handling utilities for PasaBuy Pal
 * Provides comprehensive error logging, user-friendly messages, and monitoring hooks
 */

import { AppwriteException } from 'appwrite'

export interface ErrorContext {
  userId?: string
  action?: string
  resource?: string
  metadata?: Record<string, any>
  timestamp?: string
}

export interface ErrorReport {
  id: string
  message: string
  userMessage: string
  code?: string | number
  context?: ErrorContext
  stack?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
}

/**
 * Enhanced error class with context and user-friendly messaging
 */
export class PasaBuyError extends Error {
  public readonly code?: string | number
  public readonly userMessage: string
  public readonly context?: ErrorContext
  public readonly severity: 'low' | 'medium' | 'high' | 'critical'

  constructor(
    message: string,
    userMessage: string,
    code?: string | number,
    context?: ErrorContext,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ) {
    super(message)
    this.name = 'PasaBuyError'
    this.code = code
    this.userMessage = userMessage
    this.context = context
    this.severity = severity
  }
}

/**
 * Appwrite-specific error handler
 */
export function handleAppwriteError(error: AppwriteException, context?: ErrorContext): PasaBuyError {
  const errorMap: Record<number, { message: string; severity: 'low' | 'medium' | 'high' | 'critical' }> = {
    400: { message: 'Invalid request. Please check your input and try again.', severity: 'low' },
    401: { message: 'You need to log in to access this feature.', severity: 'medium' },
    403: { message: 'You don\'t have permission to perform this action.', severity: 'medium' },
    404: { message: 'The requested item was not found.', severity: 'low' },
    409: { message: 'This item already exists or conflicts with existing data.', severity: 'low' },
    429: { message: 'Too many requests. Please wait a moment and try again.', severity: 'medium' },
    500: { message: 'Server error. Please try again later.', severity: 'high' },
    503: { message: 'Service temporarily unavailable. Please try again later.', severity: 'high' }
  }

  const errorInfo = errorMap[error.code] || { 
    message: 'An unexpected error occurred. Please try again.', 
    severity: 'medium' as const 
  }

  return new PasaBuyError(
    error.message,
    errorInfo.message,
    error.code,
    {
      ...context,
      timestamp: new Date().toISOString(),
      action: context?.action || 'unknown'
    },
    errorInfo.severity
  )
}

/**
 * Generic error handler for non-Appwrite errors
 */
export function handleGenericError(error: Error, context?: ErrorContext): PasaBuyError {
  // Network errors
  if (error.message.includes('fetch') || error.message.includes('network')) {
    return new PasaBuyError(
      error.message,
      'Network error. Please check your connection and try again.',
      'NETWORK_ERROR',
      context,
      'medium'
    )
  }

  // Validation errors
  if (error.message.includes('validation') || error.message.includes('invalid')) {
    return new PasaBuyError(
      error.message,
      'Please check your input and try again.',
      'VALIDATION_ERROR',
      context,
      'low'
    )
  }

  // Default error
  return new PasaBuyError(
    error.message,
    'An unexpected error occurred. Please try again.',
    'UNKNOWN_ERROR',
    context,
    'medium'
  )
}

/**
 * Main error handler that routes to appropriate handlers
 */
export function handleError(error: unknown, context?: ErrorContext): PasaBuyError {
  if (error instanceof AppwriteException) {
    return handleAppwriteError(error, context)
  }
  
  if (error instanceof PasaBuyError) {
    return error
  }
  
  if (error instanceof Error) {
    return handleGenericError(error, context)
  }
  
  // Handle non-Error objects
  const message = typeof error === 'string' ? error : 'An unknown error occurred'
  return new PasaBuyError(
    message,
    'An unexpected error occurred. Please try again.',
    'UNKNOWN_ERROR',
    context,
    'medium'
  )
}

/**
 * Error logging utility
 */
export function logError(error: PasaBuyError): void {
  const errorReport: ErrorReport = {
    id: generateErrorId(),
    message: error.message,
    userMessage: error.userMessage,
    code: error.code,
    context: error.context,
    stack: error.stack,
    severity: error.severity
  }

  // Console logging for development
  if (process.env.NODE_ENV === 'development') {
    console.group(`🚨 PasaBuy Error [${error.severity.toUpperCase()}]`)
    console.error('Message:', error.message)
    console.error('User Message:', error.userMessage)
    console.error('Code:', error.code)
    console.error('Context:', error.context)
    console.error('Stack:', error.stack)
    console.groupEnd()
  }

  // Production logging (can be extended with external services)
  if (process.env.NODE_ENV === 'production') {
    // Log critical and high severity errors
    if (error.severity === 'critical' || error.severity === 'high') {
      console.error('PasaBuy Error:', errorReport)
      
      // TODO: Send to external monitoring service (e.g., Sentry, LogRocket)
      // sendToMonitoringService(errorReport)
    }
  }
}

/**
 * Generate unique error ID for tracking
 */
function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Retry utility for transient errors
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  context?: ErrorContext
): Promise<T> {
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))

      if (attempt === maxRetries) {
        break
      }

      // Only retry on certain types of errors
      if (error instanceof AppwriteException) {
        // Retry on server errors and rate limits
        if (error.code === 500 || error.code === 503 || error.code === 429) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt))
          continue
        }
      }

      // Don't retry on client errors
      break
    }
  }

  throw handleError(lastError || new Error('Operation failed'), { ...context, action: `${context?.action}_retry_failed` })
}

/**
 * Safe async operation wrapper
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  context?: ErrorContext
): Promise<{ data?: T; error?: PasaBuyError }> {
  try {
    const data = await operation()
    return { data }
  } catch (error) {
    const handledError = handleError(error, context)
    logError(handledError)
    return { error: handledError }
  }
}

/**
 * Error boundary helper for React components
 */
export function createErrorBoundaryHandler(componentName: string) {
  return (error: Error, errorInfo: { componentStack: string }) => {
    const handledError = handleError(error, {
      action: 'component_error',
      resource: componentName,
      metadata: { componentStack: errorInfo.componentStack }
    })
    
    logError(handledError)
  }
}
