/**
 * Production security utilities for PasaBuy Pal
 * Provides input validation, sanitization, and security headers
 */

import { envConfig } from '../config/environment'

/**
 * Input validation patterns
 */
export const ValidationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithSpaces: /^[a-zA-Z0-9\s]+$/,
  storeCode: /^[A-Z0-9]{2,10}$/,
  currency: /^\d+(\.\d{1,2})?$/,
  url: /^https?:\/\/.+/,
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
}

/**
 * Input sanitization utilities
 */
export class InputSanitizer {
  /**
   * Sanitize string input
   */
  static sanitizeString(input: string, maxLength: number = 1000): string {
    if (typeof input !== 'string') {
      return ''
    }
    
    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
  }

  /**
   * Sanitize email input
   */
  static sanitizeEmail(email: string): string {
    const sanitized = this.sanitizeString(email, 254).toLowerCase()
    return ValidationPatterns.email.test(sanitized) ? sanitized : ''
  }

  /**
   * Sanitize phone number
   */
  static sanitizePhone(phone: string): string {
    const sanitized = this.sanitizeString(phone, 20)
    return ValidationPatterns.phone.test(sanitized) ? sanitized : ''
  }

  /**
   * Sanitize store code
   */
  static sanitizeStoreCode(code: string): string {
    const sanitized = this.sanitizeString(code, 10).toUpperCase()
    return ValidationPatterns.storeCode.test(sanitized) ? sanitized : ''
  }

  /**
   * Sanitize currency amount
   */
  static sanitizeCurrency(amount: string): string {
    const sanitized = this.sanitizeString(amount, 20)
    return ValidationPatterns.currency.test(sanitized) ? sanitized : '0'
  }

  /**
   * Sanitize URL
   */
  static sanitizeUrl(url: string): string {
    const sanitized = this.sanitizeString(url, 2048)
    return ValidationPatterns.url.test(sanitized) ? sanitized : ''
  }
}

/**
 * Input validation utilities
 */
export class InputValidator {
  /**
   * Validate required field
   */
  static required(value: any, fieldName: string): string | null {
    if (value === null || value === undefined || value === '') {
      return `${fieldName} is required`
    }
    return null
  }

  /**
   * Validate email format
   */
  static email(email: string): string | null {
    if (!email) return 'Email is required'
    if (!ValidationPatterns.email.test(email)) {
      return 'Please enter a valid email address'
    }
    return null
  }

  /**
   * Validate password strength
   */
  static password(password: string): string | null {
    if (!password) return 'Password is required'
    if (password.length < 8) {
      return 'Password must be at least 8 characters long'
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter'
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter'
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number'
    }
    return null
  }

  /**
   * Validate phone number
   */
  static phone(phone: string): string | null {
    if (!phone) return null // Phone is optional
    if (!ValidationPatterns.phone.test(phone)) {
      return 'Please enter a valid phone number'
    }
    return null
  }

  /**
   * Validate store code
   */
  static storeCode(code: string): string | null {
    if (!code) return 'Store code is required'
    if (!ValidationPatterns.storeCode.test(code)) {
      return 'Store code must be 2-10 alphanumeric characters'
    }
    return null
  }

  /**
   * Validate currency amount
   */
  static currency(amount: string): string | null {
    if (!amount) return 'Amount is required'
    if (!ValidationPatterns.currency.test(amount)) {
      return 'Please enter a valid amount'
    }
    const num = parseFloat(amount)
    if (num < 0) {
      return 'Amount cannot be negative'
    }
    if (num > 999999.99) {
      return 'Amount is too large'
    }
    return null
  }

  /**
   * Validate string length
   */
  static maxLength(value: string, max: number, fieldName: string): string | null {
    if (value && value.length > max) {
      return `${fieldName} must be ${max} characters or less`
    }
    return null
  }

  /**
   * Validate minimum length
   */
  static minLength(value: string, min: number, fieldName: string): string | null {
    if (value && value.length < min) {
      return `${fieldName} must be at least ${min} characters`
    }
    return null
  }
}

/**
 * Security headers utility
 */
export class SecurityHeaders {
  /**
   * Get Content Security Policy header
   */
  static getCSP(): string {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://cloud.appwrite.io https://*.appwrite.io",
      "frame-src 'none'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ]
    
    return directives.join('; ')
  }

  /**
   * Get all security headers
   */
  static getAllHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
    }

    if (envConfig.shouldEnableCSP) {
      headers['Content-Security-Policy'] = this.getCSP()
    }

    if (envConfig.shouldEnableHSTS) {
      headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'
    }

    return headers
  }
}

/**
 * Rate limiting utility
 */
export class RateLimiter {
  private static requests = new Map<string, { count: number; resetTime: number }>()

  /**
   * Check if request is within rate limit
   */
  static isAllowed(
    identifier: string,
    maxRequests: number = 100,
    windowMs: number = 900000 // 15 minutes
  ): boolean {
    const now = Date.now()
    const windowStart = now - windowMs
    
    // Clean up old entries
    for (const [key, value] of this.requests.entries()) {
      if (value.resetTime < now) {
        this.requests.delete(key)
      }
    }
    
    const current = this.requests.get(identifier)
    
    if (!current) {
      this.requests.set(identifier, { count: 1, resetTime: now + windowMs })
      return true
    }
    
    if (current.resetTime < now) {
      this.requests.set(identifier, { count: 1, resetTime: now + windowMs })
      return true
    }
    
    if (current.count >= maxRequests) {
      return false
    }
    
    current.count++
    return true
  }

  /**
   * Get remaining requests for identifier
   */
  static getRemaining(identifier: string, maxRequests: number = 100): number {
    const current = this.requests.get(identifier)
    if (!current || current.resetTime < Date.now()) {
      return maxRequests
    }
    return Math.max(0, maxRequests - current.count)
  }
}

/**
 * Session security utilities
 */
export class SessionSecurity {
  /**
   * Generate secure session token
   */
  static generateToken(): string {
    const array = new Uint8Array(32)
    if (typeof window !== 'undefined' && window.crypto) {
      window.crypto.getRandomValues(array)
    } else {
      // Fallback for server-side
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256)
      }
    }
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Check if session is expired
   */
  static isSessionExpired(sessionStart: number, timeout: number = envConfig.sessionTimeout): boolean {
    return Date.now() - sessionStart > timeout
  }

  /**
   * Validate session token format
   */
  static isValidTokenFormat(token: string): boolean {
    return /^[a-f0-9]{64}$/.test(token)
  }
}

/**
 * Data encryption utilities (for sensitive data)
 */
export class DataEncryption {
  /**
   * Simple base64 encoding (not for sensitive data)
   */
  static encode(data: string): string {
    if (typeof window !== 'undefined') {
      return btoa(data)
    } else {
      return Buffer.from(data).toString('base64')
    }
  }

  /**
   * Simple base64 decoding
   */
  static decode(encoded: string): string {
    try {
      if (typeof window !== 'undefined') {
        return atob(encoded)
      } else {
        return Buffer.from(encoded, 'base64').toString()
      }
    } catch {
      return ''
    }
  }

  /**
   * Hash sensitive data (one-way)
   */
  static async hash(data: string): Promise<string> {
    if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
      const encoder = new TextEncoder()
      const dataBuffer = encoder.encode(data)
      const hashBuffer = await window.crypto.subtle.digest('SHA-256', dataBuffer)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
    } else {
      // Fallback for environments without crypto.subtle
      return this.encode(data) // Not secure, but better than nothing
    }
  }
}
