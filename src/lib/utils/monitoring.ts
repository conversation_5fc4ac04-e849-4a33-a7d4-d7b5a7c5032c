/**
 * Production monitoring and performance utilities for PasaBuy Pal
 * Provides performance tracking, user analytics, and system health monitoring
 */

export interface PerformanceMetric {
  name: string
  value: number
  unit: 'ms' | 'bytes' | 'count' | 'percentage'
  timestamp: number
  context?: Record<string, any>
}

export interface UserAction {
  action: string
  resource?: string
  userId?: string
  timestamp: number
  metadata?: Record<string, any>
}

export interface SystemHealth {
  timestamp: number
  appwrite: {
    status: 'healthy' | 'degraded' | 'down'
    responseTime?: number
    lastCheck: number
  }
  performance: {
    memoryUsage?: number
    loadTime?: number
    errorRate?: number
  }
}

/**
 * Performance monitoring class
 */
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private readonly maxMetrics = 1000

  /**
   * Start timing an operation
   */
  startTiming(name: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      this.recordMetric({
        name,
        value: duration,
        unit: 'ms',
        timestamp: Date.now()
      })
    }
  }

  /**
   * Record a custom metric
   */
  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric)
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // Log slow operations in development
    if (process.env.NODE_ENV === 'development' && metric.unit === 'ms' && metric.value > 1000) {
      console.warn(`⚠️ Slow operation detected: ${metric.name} took ${metric.value}ms`)
    }
  }

  /**
   * Get performance summary
   */
  getSummary(timeWindow: number = 300000): Record<string, any> {
    const cutoff = Date.now() - timeWindow
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff)
    
    const summary: Record<string, any> = {}
    
    recentMetrics.forEach(metric => {
      if (!summary[metric.name]) {
        summary[metric.name] = {
          count: 0,
          total: 0,
          min: Infinity,
          max: -Infinity,
          unit: metric.unit
        }
      }
      
      const stat = summary[metric.name]
      stat.count++
      stat.total += metric.value
      stat.min = Math.min(stat.min, metric.value)
      stat.max = Math.max(stat.max, metric.value)
      stat.average = stat.total / stat.count
    })
    
    return summary
  }

  /**
   * Clear metrics
   */
  clear(): void {
    this.metrics = []
  }
}

/**
 * User analytics class
 */
class UserAnalytics {
  private actions: UserAction[] = []
  private readonly maxActions = 500

  /**
   * Track user action
   */
  trackAction(action: UserAction): void {
    this.actions.push(action)
    
    // Keep only the most recent actions
    if (this.actions.length > this.maxActions) {
      this.actions = this.actions.slice(-this.maxActions)
    }

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 User Action: ${action.action}`, action)
    }
  }

  /**
   * Get user action summary
   */
  getActionSummary(timeWindow: number = 3600000): Record<string, number> {
    const cutoff = Date.now() - timeWindow
    const recentActions = this.actions.filter(a => a.timestamp > cutoff)
    
    const summary: Record<string, number> = {}
    recentActions.forEach(action => {
      summary[action.action] = (summary[action.action] || 0) + 1
    })
    
    return summary
  }

  /**
   * Clear actions
   */
  clear(): void {
    this.actions = []
  }
}

/**
 * System health monitor
 */
class HealthMonitor {
  private healthStatus: SystemHealth = {
    timestamp: Date.now(),
    appwrite: {
      status: 'healthy',
      lastCheck: Date.now()
    },
    performance: {}
  }

  /**
   * Check Appwrite health
   */
  async checkAppwriteHealth(): Promise<void> {
    const startTime = performance.now()
    
    try {
      // Simple health check - try to get account info
      const response = await fetch('/api/health/appwrite', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      if (response.ok) {
        this.healthStatus.appwrite = {
          status: responseTime > 5000 ? 'degraded' : 'healthy',
          responseTime,
          lastCheck: Date.now()
        }
      } else {
        this.healthStatus.appwrite = {
          status: 'degraded',
          responseTime,
          lastCheck: Date.now()
        }
      }
    } catch (error) {
      this.healthStatus.appwrite = {
        status: 'down',
        lastCheck: Date.now()
      }
    }
  }

  /**
   * Update performance metrics
   */
  updatePerformanceMetrics(): void {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      if (navigation) {
        this.healthStatus.performance = {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          memoryUsage: (performance as any).memory?.usedJSHeapSize || 0
        }
      }
    }
  }

  /**
   * Get current health status
   */
  getHealthStatus(): SystemHealth {
    return { ...this.healthStatus, timestamp: Date.now() }
  }
}

// Global instances
export const performanceMonitor = new PerformanceMonitor()
export const userAnalytics = new UserAnalytics()
export const healthMonitor = new HealthMonitor()

/**
 * Decorator for timing function execution
 */
export function timed(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const timerName = name || `${target.constructor.name}.${propertyKey}`
    
    descriptor.value = async function (...args: any[]) {
      const stopTimer = performanceMonitor.startTiming(timerName)
      
      try {
        const result = await originalMethod.apply(this, args)
        return result
      } finally {
        stopTimer()
      }
    }
    
    return descriptor
  }
}

/**
 * Track API call performance
 */
export async function trackApiCall<T>(
  name: string,
  operation: () => Promise<T>,
  context?: Record<string, any>
): Promise<T> {
  const stopTimer = performanceMonitor.startTiming(`api.${name}`)
  
  try {
    const result = await operation()
    
    // Track successful API call
    userAnalytics.trackAction({
      action: `api.${name}.success`,
      timestamp: Date.now(),
      metadata: context
    })
    
    return result
  } catch (error) {
    // Track failed API call
    userAnalytics.trackAction({
      action: `api.${name}.error`,
      timestamp: Date.now(),
      metadata: { ...context, error: String(error) }
    })
    
    throw error
  } finally {
    stopTimer()
  }
}

/**
 * Track user interaction
 */
export function trackUserInteraction(
  action: string,
  resource?: string,
  metadata?: Record<string, any>
): void {
  userAnalytics.trackAction({
    action,
    resource,
    timestamp: Date.now(),
    metadata
  })
}

/**
 * Initialize monitoring
 */
export function initializeMonitoring(): void {
  // Check health periodically
  if (typeof window !== 'undefined') {
    // Initial health check
    healthMonitor.checkAppwriteHealth()
    healthMonitor.updatePerformanceMetrics()
    
    // Periodic health checks (every 5 minutes)
    setInterval(() => {
      healthMonitor.checkAppwriteHealth()
      healthMonitor.updatePerformanceMetrics()
    }, 300000)
    
    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      trackUserInteraction('page.visibility_change', undefined, {
        hidden: document.hidden
      })
    })
    
    // Track page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navigation) {
          performanceMonitor.recordMetric({
            name: 'page.load_time',
            value: navigation.loadEventEnd - navigation.loadEventStart,
            unit: 'ms',
            timestamp: Date.now()
          })
        }
      }, 0)
    })
  }
}

/**
 * Get monitoring dashboard data
 */
export function getMonitoringDashboard(): {
  performance: Record<string, any>
  userActions: Record<string, number>
  health: SystemHealth
} {
  return {
    performance: performanceMonitor.getSummary(),
    userActions: userAnalytics.getActionSummary(),
    health: healthMonitor.getHealthStatus()
  }
}
