/**
 * Production-ready environment configuration for PasaBuy Pal
 * Provides type-safe environment variable access with validation
 */

export interface AppConfig {
  app: {
    name: string
    version: string
    url: string
    environment: 'development' | 'production' | 'test'
  }
  appwrite: {
    endpoint: string
    projectId: string
    apiKey: string
    databaseId: string
    storageBucketId: string
  }
  features: {
    enableAnalytics: boolean
    enableErrorReporting: boolean
    enablePerformanceMonitoring: boolean
    enableDebugMode: boolean
  }
  security: {
    enableCSP: boolean
    enableHSTS: boolean
    sessionTimeout: number
  }
}

/**
 * Environment variable validation schema
 */
const envSchema = {
  // Required variables
  required: [
    'NEXT_PUBLIC_APPWRITE_ENDPOINT',
    'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
    'APPWRITE_API_KEY',
    'APPWRITE_DATABASE_ID',
    'APPWRITE_STORAGE_BUCKET_ID'
  ],
  // Optional variables with defaults
  optional: [
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_ENABLE_ANALYTICS',
    'NEXT_PUBLIC_ENABLE_ERROR_REPORTING',
    'NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING',
    'NEXT_PUBLIC_DEBUG_MODE',
    'NEXT_PUBLIC_ENABLE_CSP',
    'NEXT_PUBLIC_ENABLE_HSTS',
    'SESSION_TIMEOUT'
  ]
}

/**
 * Validate environment variables
 */
function validateEnvironment(): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Check required variables
  envSchema.required.forEach(varName => {
    if (!process.env[varName]) {
      errors.push(`Missing required environment variable: ${varName}`)
    }
  })
  
  // Validate URL format
  const endpoint = process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT
  if (endpoint && !isValidUrl(endpoint)) {
    errors.push('NEXT_PUBLIC_APPWRITE_ENDPOINT must be a valid URL')
  }
  
  const appUrl = process.env.NEXT_PUBLIC_APP_URL
  if (appUrl && !isValidUrl(appUrl)) {
    errors.push('NEXT_PUBLIC_APP_URL must be a valid URL')
  }
  
  // Validate project ID format
  const projectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID
  if (projectId && projectId.length < 5) {
    errors.push('NEXT_PUBLIC_APPWRITE_PROJECT_ID must be at least 5 characters')
  }
  
  // Validate API key format
  const apiKey = process.env.APPWRITE_API_KEY
  if (apiKey && apiKey.length < 10) {
    errors.push('APPWRITE_API_KEY must be at least 10 characters')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Helper function to validate URLs
 */
function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}

/**
 * Helper function to parse boolean environment variables
 */
function parseBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
  if (!value) return defaultValue
  return value.toLowerCase() === 'true' || value === '1'
}

/**
 * Helper function to parse number environment variables
 */
function parseNumber(value: string | undefined, defaultValue: number): number {
  if (!value) return defaultValue
  const parsed = parseInt(value, 10)
  return isNaN(parsed) ? defaultValue : parsed
}

/**
 * Get application configuration
 */
export function getAppConfig(): AppConfig {
  // Validate environment first
  const validation = validateEnvironment()
  
  if (!validation.isValid) {
    console.error('❌ Environment validation failed:')
    validation.errors.forEach(error => console.error(`  - ${error}`))
    
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Invalid environment configuration')
    }
  }
  
  return {
    app: {
      name: 'PasaBuy Pal',
      version: process.env.npm_package_version || '1.0.0',
      url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
      environment: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development'
    },
    appwrite: {
      endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
      projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '',
      apiKey: process.env.APPWRITE_API_KEY || '',
      databaseId: process.env.APPWRITE_DATABASE_ID || '',
      storageBucketId: process.env.APPWRITE_STORAGE_BUCKET_ID || ''
    },
    features: {
      enableAnalytics: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_ANALYTICS, true),
      enableErrorReporting: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_ERROR_REPORTING, true),
      enablePerformanceMonitoring: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING, true),
      enableDebugMode: parseBoolean(process.env.NEXT_PUBLIC_DEBUG_MODE, process.env.NODE_ENV === 'development')
    },
    security: {
      enableCSP: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_CSP, true),
      enableHSTS: parseBoolean(process.env.NEXT_PUBLIC_ENABLE_HSTS, process.env.NODE_ENV === 'production'),
      sessionTimeout: parseNumber(process.env.SESSION_TIMEOUT, 3600000) // 1 hour default
    }
  }
}

/**
 * Get environment-specific configuration
 */
export function getEnvironmentConfig() {
  const config = getAppConfig()
  
  return {
    isDevelopment: config.app.environment === 'development',
    isProduction: config.app.environment === 'production',
    isTest: config.app.environment === 'test',
    
    // Feature flags
    shouldEnableAnalytics: config.features.enableAnalytics && config.app.environment === 'production',
    shouldEnableErrorReporting: config.features.enableErrorReporting,
    shouldEnablePerformanceMonitoring: config.features.enablePerformanceMonitoring,
    shouldEnableDebugMode: config.features.enableDebugMode,
    
    // Security settings
    shouldEnableCSP: config.security.enableCSP,
    shouldEnableHSTS: config.security.enableHSTS && config.app.environment === 'production',
    
    // URLs
    appUrl: config.app.url,
    appwriteEndpoint: config.appwrite.endpoint,
    
    // Timeouts
    sessionTimeout: config.security.sessionTimeout
  }
}

/**
 * Validate configuration on startup
 */
export function validateConfigOnStartup(): void {
  const validation = validateEnvironment()
  
  if (!validation.isValid) {
    console.error('🚨 Configuration validation failed!')
    validation.errors.forEach(error => console.error(`  ❌ ${error}`))
    
    if (process.env.NODE_ENV === 'production') {
      process.exit(1)
    }
  } else {
    console.log('✅ Configuration validation passed')
  }
}

/**
 * Get configuration summary for debugging
 */
export function getConfigSummary(): Record<string, any> {
  const config = getAppConfig()
  
  return {
    app: config.app,
    appwrite: {
      endpoint: config.appwrite.endpoint,
      projectId: config.appwrite.projectId,
      databaseId: config.appwrite.databaseId,
      storageBucketId: config.appwrite.storageBucketId,
      apiKeyConfigured: !!config.appwrite.apiKey
    },
    features: config.features,
    security: {
      enableCSP: config.security.enableCSP,
      enableHSTS: config.security.enableHSTS,
      sessionTimeout: config.security.sessionTimeout
    }
  }
}

// Export singleton instance
export const appConfig = getAppConfig()
export const envConfig = getEnvironmentConfig()

// Validate on module load in production
if (typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
  validateConfigOnStartup()
}
