/**
 * Production API middleware for PasaBuy Pal
 * Provides authentication, rate limiting, security headers, and error handling
 */

import { NextRequest, NextResponse } from 'next/server'
import { handleError, safeAsync } from '../utils/error-handler'
import { SecurityHeaders, RateLimiter } from '../utils/security'
import { trackApiCall } from '../utils/monitoring'
import { envConfig } from '../config/environment'

export interface ApiContext {
  userId?: string
  sessionId?: string
  userAgent?: string
  ip?: string
  method: string
  path: string
  timestamp: number
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    code?: string | number
    details?: any
  }
  meta?: {
    timestamp: string
    requestId: string
    version: string
  }
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  return realIP || remoteAddr || 'unknown'
}

/**
 * Create API context from request
 */
function createApiContext(request: NextRequest): ApiContext {
  return {
    userAgent: request.headers.get('user-agent') || 'unknown',
    ip: getClientIP(request),
    method: request.method,
    path: request.nextUrl.pathname,
    timestamp: Date.now()
  }
}

/**
 * Apply security headers to response
 */
function applySecurityHeaders(response: NextResponse): NextResponse {
  const headers = SecurityHeaders.getAllHeaders()
  
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  
  return response
}

/**
 * Create standardized API response
 */
export function createApiResponse<T>(
  data?: T,
  error?: { message: string; code?: string | number; details?: any },
  requestId?: string
): ApiResponse<T> {
  return {
    success: !error,
    data: error ? undefined : data,
    error,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: requestId || generateRequestId(),
      version: envConfig.isDevelopment ? 'dev' : '1.0.0'
    }
  }
}

/**
 * API middleware wrapper
 */
export function withApiMiddleware<T = any>(
  handler: (request: NextRequest, context: ApiContext) => Promise<T>,
  options: {
    requireAuth?: boolean
    rateLimit?: { maxRequests: number; windowMs: number }
    validateMethod?: string[]
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const requestId = generateRequestId()
    const context = createApiContext(request)
    
    try {
      // Method validation
      if (options.validateMethod && !options.validateMethod.includes(request.method)) {
        return NextResponse.json(
          createApiResponse(undefined, {
            message: `Method ${request.method} not allowed`,
            code: 'METHOD_NOT_ALLOWED'
          }, requestId),
          { status: 405 }
        )
      }

      // Rate limiting
      if (options.rateLimit) {
        const identifier = context.ip || 'unknown'
        const isAllowed = RateLimiter.isAllowed(
          identifier,
          options.rateLimit.maxRequests,
          options.rateLimit.windowMs
        )
        
        if (!isAllowed) {
          return NextResponse.json(
            createApiResponse(undefined, {
              message: 'Too many requests. Please try again later.',
              code: 'RATE_LIMIT_EXCEEDED'
            }, requestId),
            { status: 429 }
          )
        }
      }

      // Authentication check
      if (options.requireAuth) {
        const authHeader = request.headers.get('authorization')
        const sessionCookie = request.cookies.get('session')
        
        if (!authHeader && !sessionCookie) {
          return NextResponse.json(
            createApiResponse(undefined, {
              message: 'Authentication required',
              code: 'UNAUTHORIZED'
            }, requestId),
            { status: 401 }
          )
        }
        
        // TODO: Validate session/token with Appwrite
        // For now, we'll rely on Appwrite SDK's built-in auth
      }

      // Execute handler with monitoring
      const result = await trackApiCall(
        `${context.method} ${context.path}`,
        () => handler(request, context),
        { requestId, ...context }
      )

      // Create successful response
      const response = NextResponse.json(
        createApiResponse(result, undefined, requestId),
        { status: 200 }
      )

      return applySecurityHeaders(response)

    } catch (error) {
      // Handle errors
      const handledError = handleError(error, {
        action: 'api_request',
        resource: context.path,
        metadata: { requestId, ...context }
      })

      const statusCode = getErrorStatusCode(handledError.code)
      
      const response = NextResponse.json(
        createApiResponse(undefined, {
          message: handledError.userMessage,
          code: handledError.code,
          details: envConfig.isDevelopment ? handledError.message : undefined
        }, requestId),
        { status: statusCode }
      )

      return applySecurityHeaders(response)
    }
  }
}

/**
 * Get HTTP status code from error code
 */
function getErrorStatusCode(code?: string | number): number {
  if (typeof code === 'number') {
    return code
  }
  
  switch (code) {
    case 'UNAUTHORIZED':
      return 401
    case 'FORBIDDEN':
      return 403
    case 'NOT_FOUND':
      return 404
    case 'METHOD_NOT_ALLOWED':
      return 405
    case 'VALIDATION_ERROR':
      return 400
    case 'RATE_LIMIT_EXCEEDED':
      return 429
    case 'NETWORK_ERROR':
      return 503
    default:
      return 500
  }
}

/**
 * Middleware for handling CORS
 */
export function withCORS(response: NextResponse, origin?: string): NextResponse {
  const allowedOrigins = [
    envConfig.appUrl,
    'http://localhost:3000',
    'http://localhost:3001'
  ]
  
  const requestOrigin = origin || envConfig.appUrl
  
  if (allowedOrigins.includes(requestOrigin)) {
    response.headers.set('Access-Control-Allow-Origin', requestOrigin)
  }
  
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  response.headers.set('Access-Control-Allow-Credentials', 'true')
  response.headers.set('Access-Control-Max-Age', '86400')
  
  return response
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function handleCORSPreflight(request: NextRequest): NextResponse {
  const response = new NextResponse(null, { status: 200 })
  return withCORS(response, request.headers.get('origin') || undefined)
}

/**
 * Middleware for request logging
 */
export function withRequestLogging<T>(
  handler: (request: NextRequest, context: ApiContext) => Promise<T>
) {
  return async (request: NextRequest, context: ApiContext): Promise<T> => {
    const startTime = Date.now()
    
    if (envConfig.isDevelopment) {
      console.log(`🔄 ${context.method} ${context.path} - ${context.ip}`)
    }
    
    try {
      const result = await handler(request, context)
      
      const duration = Date.now() - startTime
      
      if (envConfig.isDevelopment) {
        console.log(`✅ ${context.method} ${context.path} - ${duration}ms`)
      }
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      
      if (envConfig.isDevelopment) {
        console.log(`❌ ${context.method} ${context.path} - ${duration}ms - ${error}`)
      }
      
      throw error
    }
  }
}

/**
 * Middleware for input validation
 */
export function withInputValidation<T>(
  handler: (request: NextRequest, context: ApiContext) => Promise<T>,
  validator: (data: any) => { isValid: boolean; errors: string[] }
) {
  return async (request: NextRequest, context: ApiContext): Promise<T> => {
    if (request.method === 'POST' || request.method === 'PUT') {
      const { data: body, error } = await safeAsync(
        () => request.json(),
        { action: 'parse_request_body' }
      )
      
      if (error) {
        throw new Error('Invalid JSON in request body')
      }
      
      const validation = validator(body)
      
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
      }
    }
    
    return handler(request, context)
  }
}

/**
 * Combine multiple middleware functions
 */
export function combineMiddleware<T>(
  ...middlewares: Array<(handler: any) => any>
) {
  return (handler: (request: NextRequest, context: ApiContext) => Promise<T>) => {
    return middlewares.reduce((acc, middleware) => middleware(acc), handler)
  }
}
