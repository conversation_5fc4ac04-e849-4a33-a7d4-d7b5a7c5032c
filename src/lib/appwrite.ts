import { Client, Databases, Storage, Account, Query, Permission, Role, ID } from 'appwrite'
import { Client as NodeClient, Databases as NodeDatabases, Storage as NodeStorage, Users as NodeUsers } from 'node-appwrite'

// Environment validation
const requiredEnvVars = {
  NEXT_PUBLIC_APPWRITE_ENDPOINT: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT,
  NEXT_PUBLIC_APPWRITE_PROJECT_ID: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
  APPWRITE_API_KEY: process.env.APPWRITE_API_KEY,
  APPWRITE_DATABASE_ID: process.env.APPWRITE_DATABASE_ID,
  APPWRITE_STORAGE_BUCKET_ID: process.env.APPWRITE_STORAGE_BUCKET_ID
}

// Validate required environment variables
Object.entries(requiredEnvVars).forEach(([key, value]) => {
  if (!value && typeof window === 'undefined') {
    console.warn(`⚠️ Missing environment variable: ${key}`)
  }
})

// Client-side Appwrite configuration with error handling
export const client = new Client()

try {
  client
    .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
    .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '')
} catch (error) {
  console.error('Failed to initialize Appwrite client:', error)
}

// Server-side Appwrite configuration with error handling
export const serverClient = new NodeClient()

try {
  serverClient
    .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
    .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '')
    .setKey(process.env.APPWRITE_API_KEY || '')
} catch (error) {
  console.error('Failed to initialize Appwrite server client:', error)
}

// Client-side services
export const databases = new Databases(client)
export const storage = new Storage(client)
export const account = new Account(client)

// Server-side services
export const serverDatabases = new NodeDatabases(serverClient)
export const serverStorage = new NodeStorage(serverClient)
export const serverUsers = new NodeUsers(serverClient)

// Configuration constants
export const DATABASE_ID = process.env.APPWRITE_DATABASE_ID || ''
export const STORAGE_BUCKET_ID = process.env.APPWRITE_STORAGE_BUCKET_ID || ''

// Collection IDs (will be created during migration)
export const COLLECTIONS = {
  STORES: 'stores',
  CUSTOMERS: 'customers', 
  ORDERS: 'orders',
  INVOICES: 'invoices',
  INVOICE_ITEMS: 'invoice_items',
  STORE_PRICING: 'store_pricing',
  PRICING_TIERS: 'pricing_tiers',
  CUSTOMER_ADDRESSES: 'customer_addresses',
  CUSTOMER_COMMUNICATIONS: 'customer_communications',
  ORDER_ATTACHMENTS: 'order_attachments',
  ORDER_METRICS: 'order_metrics',
  ORDER_NOTES: 'order_notes',
  ORDER_STATUS_HISTORY: 'order_status_history',
  ORDER_TAGS: 'order_tags',
  ORDER_TIMELINE: 'order_timeline',
  INVOICE_ATTACHMENTS: 'invoice_attachments',
  INVOICE_COMMUNICATIONS: 'invoice_communications',
  INVOICE_PAYMENTS: 'invoice_payments',
  STORE_CONFIGURATIONS: 'store_configurations'
} as const

// Type definitions for Appwrite documents
export interface AppwriteDocument {
  $id: string
  $createdAt: string
  $updatedAt: string
  $permissions: string[]
  $collectionId: string
  $databaseId: string
}

// Helper function to convert Prisma data to Appwrite format
export function convertToAppwriteDocument<T extends Record<string, any>>(
  data: T,
  excludeFields: string[] = ['id', 'createdAt', 'updatedAt']
): Omit<T, typeof excludeFields[number]> {
  const converted = { ...data }
  
  // Remove fields that Appwrite handles automatically
  excludeFields.forEach(field => {
    delete converted[field]
  })
  
  return converted
}

// Helper function to convert Appwrite document to app format
export function convertFromAppwriteDocument<T>(
  doc: AppwriteDocument & Record<string, any>
): T & { id: string; createdAt: string; updatedAt: string } {
  const { $id, $createdAt, $updatedAt, $permissions, $collectionId, $databaseId, ...data } = doc
  
  return {
    ...data,
    id: $id,
    createdAt: $createdAt,
    updatedAt: $updatedAt
  } as T & { id: string; createdAt: string; updatedAt: string }
}

// Export the actual Appwrite Query, Permission, Role, and ID classes
export { Query, Permission, Role, ID }
