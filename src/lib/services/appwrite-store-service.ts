import {
  serverDatabases,
  databases,
  DATABASE_ID,
  COLLECTIONS,
  convertFromAppwriteDocument,
  convertToAppwriteDocument,
  Query,
  Permission,
  Role,
  ID
} from '../appwrite'
import { authService } from '../auth'

export interface StoreCode {
  id: string
  code: string
  name?: string
  description?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  managerName?: string
  managerEmail?: string
  contactPerson?: string
  operatingHours?: string
  timezone?: string
  isOpen?: boolean
  allowsPickup?: boolean
  allowsDelivery?: boolean
  deliveryRadius?: number
  minimumOrder?: number
  serviceFee?: number
  averageProcessingTime?: number
  capacity?: number
  priority?: string
  totalOrders?: number
  totalRevenue?: number
  averageOrderValue?: number
  notes?: string
  internalNotes?: string
  specialInstructions?: string
  externalStoreId?: string
  apiEndpoint?: string
  apiKey?: string
  parentStoreId?: string
  userId: string
  createdAt: string
  updatedAt: string
}

export interface CreateStoreCodeData {
  code: string
  name?: string
  description?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  managerName?: string
  managerEmail?: string
  contactPerson?: string
  operatingHours?: string
  timezone?: string
  isOpen?: boolean
  allowsPickup?: boolean
  allowsDelivery?: boolean
  deliveryRadius?: number
  minimumOrder?: number
  serviceFee?: number
  averageProcessingTime?: number
  capacity?: number
  priority?: string
  notes?: string
  internalNotes?: string
  specialInstructions?: string
  externalStoreId?: string
  apiEndpoint?: string
  apiKey?: string
  parentStoreId?: string
}

export interface UpdateStoreCodeData extends Partial<CreateStoreCodeData> {}

class AppwriteStoreService {
  // Get all store codes for current user
  async getStoreCodes(userId?: string): Promise<StoreCode[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      console.log('Fetching store codes with queries:', [
        `Query.equal('userId', '${currentUserId}')`
      ])

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.STORES,
        [
          Query.equal('userId', currentUserId)
        ]
      )

      console.log(`Found ${response.documents.length} store documents`)
      const storeCodes = response.documents.map(doc => {
        const converted = convertFromAppwriteDocument<StoreCode>(doc)
        console.log(`Converted store: ${converted.code} (ID: ${converted.id})`)
        return converted
      })

      return storeCodes
    } catch (error: any) {
      console.error('Error in getStoreCodes:', error)
      throw new Error(`Failed to fetch store codes: ${error.message}`)
    }
  }

  // Get store code by ID
  async getStoreCodeById(id: string, userId?: string): Promise<StoreCode | null> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.STORES,
        id
      )

      const storeCode = convertFromAppwriteDocument<StoreCode>(response)
      
      // Verify ownership
      if (storeCode.userId !== currentUserId) {
        throw new Error('Access denied')
      }

      return storeCode
    } catch (error: any) {
      if (error.code === 404) {
        return null
      }
      throw new Error(`Failed to fetch store code: ${error.message}`)
    }
  }

  // Get store code by code string
  async getStoreCodeByCode(code: string, userId?: string): Promise<StoreCode | null> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.STORES,
        [
          Query.equal('userId', currentUserId),
          Query.equal('code', code.toUpperCase())
        ]
      )

      if (response.documents.length === 0) {
        return null
      }

      return convertFromAppwriteDocument<StoreCode>(response.documents[0])
    } catch (error: any) {
      throw new Error(`Failed to fetch store code: ${error.message}`)
    }
  }

  // Create new store code
  async createStoreCode(data: CreateStoreCodeData, userId?: string): Promise<StoreCode> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Check if code already exists for this user
      const existing = await this.getStoreCodeByCode(data.code, currentUserId)
      if (existing) {
        throw new Error('Store code already exists')
      }

      const documentData = {
        ...convertToAppwriteDocument(data),
        code: data.code.toUpperCase(),
        country: data.country || 'Philippines',
        timezone: data.timezone || 'Asia/Manila',
        isOpen: data.isOpen ?? true,
        allowsPickup: data.allowsPickup ?? true,
        allowsDelivery: data.allowsDelivery ?? true,
        minimumOrder: data.minimumOrder ?? 0.00,
        serviceFee: data.serviceFee ?? 0.00,
        priority: data.priority || 'NORMAL',
        totalOrders: 0,
        totalRevenue: 0.00,
        averageOrderValue: 0.00,
        userId: currentUserId
      }

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.STORES,
        ID.unique(),
        documentData,
        [
          Permission.read(Role.user(currentUserId)),
          Permission.write(Role.user(currentUserId))
        ]
      )

      return convertFromAppwriteDocument<StoreCode>(response)
    } catch (error: any) {
      throw new Error(`Failed to create store code: ${error.message}`)
    }
  }

  // Update store code
  async updateStoreCode(id: string, data: UpdateStoreCodeData, userId?: string): Promise<StoreCode> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getStoreCodeById(id, currentUserId)
      if (!existing) {
        throw new Error('Store code not found')
      }

      const updateData: any = convertToAppwriteDocument(data)

      // If code is being updated, ensure it's uppercase and unique
      if (data.code) {
        const upperCode = data.code.toUpperCase()
        updateData.code = upperCode

        // Check if new code already exists (excluding current record)
        const codeCheck = await this.getStoreCodeByCode(upperCode, currentUserId)
        if (codeCheck && codeCheck.id !== id) {
          throw new Error('Store code already exists')
        }
      }

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.STORES,
        id,
        updateData
      )

      return convertFromAppwriteDocument<StoreCode>(response)
    } catch (error: any) {
      throw new Error(`Failed to update store code: ${error.message}`)
    }
  }

  // Delete store code
  async deleteStoreCode(id: string, userId?: string): Promise<void> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getStoreCodeById(id, currentUserId)
      if (!existing) {
        throw new Error('Store code not found')
      }

      // TODO: Check if store code is being used in orders before deletion
      // This would require querying the orders collection

      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.STORES,
        id
      )
    } catch (error: any) {
      throw new Error(`Failed to delete store code: ${error.message}`)
    }
  }

  // Search store codes
  async searchStoreCodes(query: string, userId?: string): Promise<StoreCode[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.STORES,
        [
          Query.equal('userId', currentUserId),
          Query.or([
            Query.search('code', query),
            Query.search('name', query)
          ]),
          Query.orderAsc('code')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<StoreCode>(doc))
    } catch (error: any) {
      throw new Error(`Failed to search store codes: ${error.message}`)
    }
  }

  // Get store codes with order counts
  async getStoreCodesWithCounts(userId?: string): Promise<(StoreCode & { orderCount: number })[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      console.log('Fetching store codes for user:', currentUserId)
      const storeCodes = await this.getStoreCodes(currentUserId)
      console.log('Found store codes:', storeCodes.length)

      // If no store codes, return empty array
      if (storeCodes.length === 0) {
        return []
      }

      // Get order counts for each store (only unbought orders for buy list)
      const storeCodesWithCounts = await Promise.all(
        storeCodes.map(async (store) => {
          try {
            console.log(`Counting orders for store: ${store.code} (ID: ${store.id})`)

            // Validate store.id before using in query
            if (!store.id || typeof store.id !== 'string') {
              console.warn(`Invalid store ID for ${store.code}:`, store.id)
              return {
                ...store,
                orderCount: 0
              }
            }

            const ordersResponse = await databases.listDocuments(
              DATABASE_ID,
              COLLECTIONS.ORDERS,
              [
                Query.equal('userId', currentUserId),
                Query.equal('storeCodeId', store.id),
                Query.equal('isBought', false),
                Query.limit(1000) // Reasonable limit for counting
              ]
            )

            const orderCount = ordersResponse.total || ordersResponse.documents.length
            console.log(`Store ${store.code} has ${orderCount} orders`)

            return {
              ...store,
              orderCount
            }
          } catch (error) {
            console.warn(`Failed to count orders for store ${store.code}:`, error)
            return {
              ...store,
              orderCount: 0
            }
          }
        })
      )

      return storeCodesWithCounts
    } catch (error: any) {
      console.error('Error in getStoreCodesWithCounts:', error)
      throw new Error(`Failed to fetch store codes with counts: ${error.message}`)
    }
  }
}

// Create and export service instance
export const appwriteStoreService = new AppwriteStoreService()
