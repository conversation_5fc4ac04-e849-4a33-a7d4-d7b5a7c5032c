import {
  serverDatabases,
  databases,
  DATABASE_ID,
  COLLECTIONS,
  convertFromAppwriteDocument,
  convertToAppwriteDocument,
  Query,
  Permission,
  Role,
  ID
} from '../appwrite'
import { authService } from '../auth'

export interface Invoice {
  id: string
  invoiceNumber: string
  customerId: string
  status?: string
  invoiceType?: string
  priority?: string
  subtotal?: number
  discountAmount?: number
  discountPercentage?: number
  taxAmount?: number
  taxRate?: number
  shippingCost?: number
  total?: number
  paymentTerms?: number
  paymentMethod?: string
  currency?: string
  exchangeRate?: number
  issueDate?: string
  dueDate?: string
  sentDate?: string
  paidDate?: string
  overdueDate?: string
  approvalStatus?: string
  approvedBy?: string
  approvedAt?: string
  rejectedBy?: string
  rejectedAt?: string
  rejectionReason?: string
  billingAddress?: string
  shippingAddress?: string
  customerPO?: string
  templateId?: string
  logoUrl?: string
  headerText?: string
  footerText?: string
  emailsSent?: number
  lastEmailSent?: string
  viewCount?: number
  lastViewed?: string
  notes?: string
  internalNotes?: string
  customerNotes?: string
  externalInvoiceId?: string
  syncStatus?: string
  lastSyncAt?: string
  syncError?: string
  userId: string
  createdAt: string
  updatedAt: string
}

export interface CreateInvoiceData {
  invoiceNumber: string
  customerId: string
  status?: string
  invoiceType?: string
  priority?: string
  subtotal?: number
  discountAmount?: number
  discountPercentage?: number
  taxAmount?: number
  taxRate?: number
  shippingCost?: number
  total?: number
  paymentTerms?: number
  paymentMethod?: string
  currency?: string
  exchangeRate?: number
  issueDate?: string
  dueDate?: string
  sentDate?: string
  paidDate?: string
  billingAddress?: string
  shippingAddress?: string
  customerPO?: string
  templateId?: string
  logoUrl?: string
  headerText?: string
  footerText?: string
  notes?: string
  internalNotes?: string
  customerNotes?: string
}

export interface UpdateInvoiceData extends Partial<CreateInvoiceData> {}

class AppwriteInvoiceService {
  // Get all invoices for current user
  async getInvoices(userId?: string): Promise<Invoice[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        [
          Query.equal('userId', currentUserId),
          Query.orderDesc('$createdAt')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Invoice>(doc))
    } catch (error: any) {
      throw new Error(`Failed to fetch invoices: ${error.message}`)
    }
  }

  // Get invoice by ID
  async getInvoiceById(id: string, userId?: string): Promise<Invoice | null> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        id
      )

      const invoice = convertFromAppwriteDocument<Invoice>(response)
      
      // Verify ownership
      if (invoice.userId !== currentUserId) {
        throw new Error('Access denied')
      }

      return invoice
    } catch (error: any) {
      if (error.code === 404) {
        return null
      }
      throw new Error(`Failed to fetch invoice: ${error.message}`)
    }
  }

  // Create new invoice
  async createInvoice(data: CreateInvoiceData, userId?: string): Promise<Invoice> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Check if invoice number already exists for this user
      const existing = await this.getInvoiceByNumber(data.invoiceNumber, currentUserId)
      if (existing) {
        throw new Error('Invoice number already exists')
      }

      const documentData = {
        ...convertToAppwriteDocument(data),
        status: data.status || 'DRAFT',
        invoiceType: data.invoiceType || 'STANDARD',
        priority: data.priority || 'NORMAL',
        subtotal: data.subtotal ?? 0.00,
        discountAmount: data.discountAmount ?? 0.00,
        discountPercentage: data.discountPercentage ?? 0.00,
        taxAmount: data.taxAmount ?? 0.00,
        taxRate: data.taxRate ?? 0.00,
        shippingCost: data.shippingCost ?? 0.00,
        total: data.total ?? 0.00,
        paymentTerms: data.paymentTerms ?? 30,
        currency: data.currency || 'PHP',
        exchangeRate: data.exchangeRate ?? 1.00,
        approvalStatus: 'PENDING',
        emailsSent: 0,
        viewCount: 0,
        syncStatus: 'PENDING',
        userId: currentUserId
      }

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        ID.unique(),
        documentData,
        [
          Permission.read(Role.user(currentUserId)),
          Permission.write(Role.user(currentUserId))
        ]
      )

      return convertFromAppwriteDocument<Invoice>(response)
    } catch (error: any) {
      throw new Error(`Failed to create invoice: ${error.message}`)
    }
  }

  // Update invoice
  async updateInvoice(id: string, data: UpdateInvoiceData, userId?: string): Promise<Invoice> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getInvoiceById(id, currentUserId)
      if (!existing) {
        throw new Error('Invoice not found')
      }

      const updateData = convertToAppwriteDocument(data)
      
      // If invoice number is being updated, ensure it's unique
      if (data.invoiceNumber && data.invoiceNumber !== existing.invoiceNumber) {
        const numberCheck = await this.getInvoiceByNumber(data.invoiceNumber, currentUserId)
        if (numberCheck && numberCheck.id !== id) {
          throw new Error('Invoice number already exists')
        }
      }

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        id,
        updateData
      )

      return convertFromAppwriteDocument<Invoice>(response)
    } catch (error: any) {
      throw new Error(`Failed to update invoice: ${error.message}`)
    }
  }

  // Delete invoice
  async deleteInvoice(id: string, userId?: string): Promise<void> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getInvoiceById(id, currentUserId)
      if (!existing) {
        throw new Error('Invoice not found')
      }

      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        id
      )
    } catch (error: any) {
      throw new Error(`Failed to delete invoice: ${error.message}`)
    }
  }

  // Get invoice by number
  async getInvoiceByNumber(invoiceNumber: string, userId?: string): Promise<Invoice | null> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        [
          Query.equal('userId', currentUserId),
          Query.equal('invoiceNumber', invoiceNumber)
        ]
      )

      if (response.documents.length === 0) {
        return null
      }

      return convertFromAppwriteDocument<Invoice>(response.documents[0])
    } catch (error: any) {
      throw new Error(`Failed to fetch invoice: ${error.message}`)
    }
  }

  // Get invoices by customer
  async getInvoicesByCustomer(customerId: string, userId?: string): Promise<Invoice[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        [
          Query.equal('userId', currentUserId),
          Query.equal('customerId', customerId),
          Query.orderDesc('$createdAt')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Invoice>(doc))
    } catch (error: any) {
      throw new Error(`Failed to fetch invoices by customer: ${error.message}`)
    }
  }

  // Get invoices by status
  async getInvoicesByStatus(status: string, userId?: string): Promise<Invoice[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        [
          Query.equal('userId', currentUserId),
          Query.equal('status', status),
          Query.orderDesc('$createdAt')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Invoice>(doc))
    } catch (error: any) {
      throw new Error(`Failed to fetch invoices by status: ${error.message}`)
    }
  }

  // Search invoices
  async searchInvoices(query: string, userId?: string): Promise<Invoice[]> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.INVOICES,
        [
          Query.equal('userId', currentUserId),
          Query.search('invoiceNumber', query),
          Query.orderDesc('$createdAt')
        ]
      )

      return response.documents.map(doc => convertFromAppwriteDocument<Invoice>(doc))
    } catch (error: any) {
      throw new Error(`Failed to search invoices: ${error.message}`)
    }
  }

  // Mark invoice as sent
  async markAsSent(id: string, userId?: string): Promise<Invoice> {
    return this.updateInvoice(id, {
      status: 'SENT',
      sentDate: new Date().toISOString()
    }, userId)
  }

  // Mark invoice as paid
  async markAsPaid(id: string, userId?: string): Promise<Invoice> {
    return this.updateInvoice(id, {
      status: 'PAID',
      paidDate: new Date().toISOString()
    }, userId)
  }

  // Get invoice statistics
  async getInvoiceStats(userId?: string): Promise<{
    total: number
    draft: number
    sent: number
    paid: number
    overdue: number
    totalAmount: number
    paidAmount: number
    outstandingAmount: number
  }> {
    try {
      const invoices = await this.getInvoices(userId)
      
      const stats = {
        total: invoices.length,
        draft: invoices.filter(i => i.status === 'DRAFT').length,
        sent: invoices.filter(i => i.status === 'SENT').length,
        paid: invoices.filter(i => i.status === 'PAID').length,
        overdue: invoices.filter(i => i.status === 'OVERDUE').length,
        totalAmount: invoices.reduce((sum, i) => sum + (i.total || 0), 0),
        paidAmount: invoices.filter(i => i.status === 'PAID').reduce((sum, i) => sum + (i.total || 0), 0),
        outstandingAmount: invoices.filter(i => i.status !== 'PAID').reduce((sum, i) => sum + (i.total || 0), 0)
      }

      return stats
    } catch (error: any) {
      throw new Error(`Failed to get invoice statistics: ${error.message}`)
    }
  }
}

// Create and export service instance
export const appwriteInvoiceService = new AppwriteInvoiceService()
