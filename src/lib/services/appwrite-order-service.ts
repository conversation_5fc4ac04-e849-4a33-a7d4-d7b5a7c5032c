import {
  serverDatabases,
  databases,
  DATABASE_ID,
  COLLECTIONS,
  convertFromAppwriteDocument,
  convertToAppwriteDocument,
  Query,
  Permission,
  Role,
  ID
} from '../appwrite'
import { authService } from '../auth'

export interface Order {
  id: string
  productName: string
  quantity?: number
  usageUnit?: string
  comment?: string
  imageFilename?: string
  storePrice?: number
  pasabuyFee?: number
  customerPrice?: number
  isBought?: boolean
  packingStatus?: string
  orderNumber?: string
  priority?: string
  category?: string
  brand?: string
  model?: string
  sku?: string
  barcode?: string
  weight?: number
  dimensions?: string
  color?: string
  size?: string
  material?: string
  condition?: string
  warranty?: string
  source?: string
  sourceUrl?: string
  urgency?: string
  specialInstructions?: string
  estimatedDelivery?: string
  requestedDelivery?: string
  completedAt?: string
  cancelledAt?: string
  cancellationReason?: string
  parentOrderId?: string
  orderGroupId?: string
  deliveryStatus?: string
  deliveryDate?: string
  deliveryMethod?: string
  trackingNumber?: string
  deliveryNotes?: string
  storeCodeId?: string
  customerId?: string
  userId: string
  createdAt: string
  updatedAt: string
}

export interface CreateOrderData {
  productName: string
  quantity?: number
  usageUnit?: string
  comment?: string
  imageFilename?: string
  storePrice?: number
  pasabuyFee?: number
  customerPrice?: number
  isBought?: boolean
  packingStatus?: string
  orderNumber?: string
  priority?: string
  category?: string
  brand?: string
  model?: string
  sku?: string
  barcode?: string
  weight?: number
  dimensions?: string
  color?: string
  size?: string
  material?: string
  condition?: string
  warranty?: string
  source?: string
  sourceUrl?: string
  urgency?: string
  specialInstructions?: string
  estimatedDelivery?: string
  requestedDelivery?: string
  parentOrderId?: string
  orderGroupId?: string
  deliveryStatus?: string
  deliveryMethod?: string
  trackingNumber?: string
  deliveryNotes?: string
  storeCodeId?: string
  customerId?: string
}

export interface UpdateOrderData extends Partial<CreateOrderData> {}

export interface OrderQueryOptions {
  page?: number
  limit?: number
  sortField?: string
  sortDirection?: 'asc' | 'desc'
  search?: string
  storeCodeId?: string
  customerId?: string
  isBought?: boolean
  packingStatus?: string
}

export interface OrdersResponse {
  data: Order[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

class AppwriteOrderService {
  // Get all orders for current user with pagination and sorting
  async getOrders(userId?: string, options: OrderQueryOptions = {}): Promise<OrdersResponse> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const {
        page = 1,
        limit = 15,
        sortField = 'createdAt',
        sortDirection = 'desc',
        search,
        storeCodeId,
        customerId,
        isBought,
        packingStatus
      } = options

      // Build queries
      const queries = [Query.equal('userId', currentUserId)]

      // Add filters
      if (search) {
        queries.push(Query.or([
          Query.search('productName', search),
          Query.search('orderNumber', search)
        ]))
      }

      if (storeCodeId) {
        queries.push(Query.equal('storeCodeId', storeCodeId))
      }

      if (customerId) {
        queries.push(Query.equal('customerId', customerId))
      }

      if (isBought !== undefined) {
        queries.push(Query.equal('isBought', isBought))
      }

      if (packingStatus) {
        queries.push(Query.equal('packingStatus', packingStatus))
      }

      // Add sorting
      const appwriteSortField = sortField === 'createdAt' ? '$createdAt' : sortField
      if (sortDirection === 'asc') {
        queries.push(Query.orderAsc(appwriteSortField))
      } else {
        queries.push(Query.orderDesc(appwriteSortField))
      }

      // Add pagination
      const offset = (page - 1) * limit
      queries.push(Query.limit(limit))
      queries.push(Query.offset(offset))

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        queries
      )

      const orders = response.documents.map(doc => convertFromAppwriteDocument<Order>(doc))

      return {
        data: orders,
        pagination: {
          page,
          limit,
          total: response.total,
          totalPages: Math.ceil(response.total / limit)
        }
      }
    } catch (error: any) {
      throw new Error(`Failed to fetch orders: ${error.message}`)
    }
  }

  // Legacy method for backward compatibility
  async getAllOrders(userId?: string): Promise<Order[]> {
    const response = await this.getOrders(userId, { limit: 1000 })
    return response.data
  }

  // Get order by ID
  async getOrderById(id: string, userId?: string): Promise<Order | null> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id
      )

      const order = convertFromAppwriteDocument<Order>(response)
      
      // Verify ownership
      if (order.userId !== currentUserId) {
        throw new Error('Access denied')
      }

      return order
    } catch (error: any) {
      if (error.code === 404) {
        return null
      }
      throw new Error(`Failed to fetch order: ${error.message}`)
    }
  }

  // Create new order
  async createOrder(data: CreateOrderData, userId?: string): Promise<Order> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      const documentData = {
        ...convertToAppwriteDocument(data),
        quantity: data.quantity ?? 1,
        storePrice: data.storePrice ?? 0.00,
        pasabuyFee: data.pasabuyFee ?? 0.00,
        customerPrice: data.customerPrice ?? 0.00,
        isBought: data.isBought ?? false,
        packingStatus: data.packingStatus || 'Not Packed',
        priority: data.priority || 'NORMAL',
        condition: data.condition || 'NEW',
        urgency: data.urgency || 'MEDIUM',
        deliveryStatus: data.deliveryStatus || 'Not Delivered',
        userId: currentUserId
      }

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        ID.unique(),
        documentData,
        [
          Permission.read(Role.user(currentUserId)),
          Permission.write(Role.user(currentUserId))
        ]
      )

      return convertFromAppwriteDocument<Order>(response)
    } catch (error: any) {
      throw new Error(`Failed to create order: ${error.message}`)
    }
  }

  // Update order
  async updateOrder(id: string, data: UpdateOrderData, userId?: string): Promise<Order> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getOrderById(id, currentUserId)
      if (!existing) {
        throw new Error('Order not found')
      }

      const updateData = convertToAppwriteDocument(data)

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id,
        updateData
      )

      return convertFromAppwriteDocument<Order>(response)
    } catch (error: any) {
      throw new Error(`Failed to update order: ${error.message}`)
    }
  }

  // Delete order
  async deleteOrder(id: string, userId?: string): Promise<void> {
    try {
      const currentUserId = userId || await authService.getUserId()
      if (!currentUserId) {
        throw new Error('User not authenticated')
      }

      // Verify ownership
      const existing = await this.getOrderById(id, currentUserId)
      if (!existing) {
        throw new Error('Order not found')
      }

      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id
      )
    } catch (error: any) {
      throw new Error(`Failed to delete order: ${error.message}`)
    }
  }

  // Get orders by store code (legacy method for backward compatibility)
  async getOrdersByStoreCode(storeCodeId: string, userId?: string): Promise<Order[]> {
    const response = await this.getOrders(userId, { storeCodeId, limit: 1000 })
    return response.data
  }

  // Get orders by customer (legacy method for backward compatibility)
  async getOrdersByCustomer(customerId: string, userId?: string): Promise<Order[]> {
    const response = await this.getOrders(userId, { customerId, limit: 1000 })
    return response.data
  }

  // Get orders by status (legacy method for backward compatibility)
  async getOrdersByStatus(isBought: boolean, packingStatus?: string, userId?: string): Promise<Order[]> {
    const response = await this.getOrders(userId, { isBought, packingStatus, limit: 1000 })
    return response.data
  }

  // Search orders (legacy method for backward compatibility)
  async searchOrders(query: string, userId?: string): Promise<Order[]> {
    const response = await this.getOrders(userId, { search: query, limit: 1000 })
    return response.data
  }

  // Get buy list (orders to buy) - legacy method
  async getBuyList(userId?: string): Promise<Order[]> {
    const response = await this.getOrders(userId, { isBought: false, limit: 1000 })
    return response.data
  }

  // Get packing list (orders to pack) - legacy method
  async getPackingList(userId?: string): Promise<Order[]> {
    const response = await this.getOrders(userId, { isBought: true, packingStatus: 'Not Packed', limit: 1000 })
    return response.data
  }

  // Mark order as bought
  async markAsBought(id: string, storePrice: number, pasabuyFee: number, customerPrice: number, userId?: string): Promise<Order> {
    return this.updateOrder(id, {
      isBought: true,
      storePrice,
      pasabuyFee,
      customerPrice
    }, userId)
  }

  // Mark order as packed
  async markAsPacked(id: string, userId?: string): Promise<Order> {
    return this.updateOrder(id, {
      packingStatus: 'Packed'
    }, userId)
  }

  // Get order statistics
  async getOrderStats(userId?: string): Promise<{
    total: number
    toBuy: number
    toPack: number
    packed: number
    totalValue: number
  }> {
    try {
      const orders = await this.getAllOrders(userId)

      const stats = {
        total: orders.length,
        toBuy: orders.filter(o => !o.isBought).length,
        toPack: orders.filter(o => o.isBought && o.packingStatus === 'Not Packed').length,
        packed: orders.filter(o => o.packingStatus === 'Packed').length,
        totalValue: orders.reduce((sum, o) => sum + (o.customerPrice || 0), 0)
      }

      return stats
    } catch (error: any) {
      throw new Error(`Failed to get order statistics: ${error.message}`)
    }
  }
}

// Create and export service instance
export const appwriteOrderService = new AppwriteOrderService()
