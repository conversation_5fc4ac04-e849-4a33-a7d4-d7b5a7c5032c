/**
 * Server-side Appwrite Order Service
 * Uses server-side Appwrite client with proper authentication context
 */

import { NextRequest } from 'next/server'
import { Client as NodeClient, Databases, Query, Permission, Role, ID } from 'node-appwrite'
import {
  DATABASE_ID,
  COLLECTIONS,
  convertFromAppwriteDocument,
  convertToAppwriteDocument
} from '../appwrite'
import { getUserIdFromRequest } from '../auth-server'
import { Order, CreateOrderData, UpdateOrderData, OrderQueryOptions, OrdersResponse } from './appwrite-order-service'

class AppwriteOrderServiceServer {
  // Create a session-based client for the authenticated user
  private createUserClient(request: NextRequest): NodeClient {
    const sessionToken = this.getSessionFromRequest(request)
    if (!sessionToken) {
      throw new Error('No session token found')
    }

    return new NodeClient()
      .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || '')
      .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '')
      .setSession(sessionToken)
  }

  // Extract session from request cookies
  private getSessionFromRequest(request: NextRequest): string | null {
    const cookies = request.cookies
    const projectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID
    const sessionCookieName = `a_session_${projectId}`

    const sessionCookie = cookies.get(sessionCookieName)
    if (sessionCookie) {
      return sessionCookie.value
    }

    // Fallback: look for any session cookie that might be Appwrite-related
    const cookieNames = Array.from(cookies.getAll().map(cookie => cookie.name))
    for (const name of cookieNames) {
      if (name.startsWith('a_session_')) {
        const cookie = cookies.get(name)
        if (cookie) {
          return cookie.value
        }
      }
    }

    return null
  }

  // Get all orders for current user with pagination and sorting
  async getOrders(request: NextRequest, options: OrderQueryOptions = {}): Promise<OrdersResponse> {
    try {
      const userId = await getUserIdFromRequest(request)
      if (!userId) {
        throw new Error('User not authenticated')
      }

      const userClient = this.createUserClient(request)
      const databases = new Databases(userClient)

      const {
        page = 1,
        limit = 15,
        sortField = 'createdAt',
        sortDirection = 'desc',
        search,
        storeCodeId,
        customerId,
        isBought,
        packingStatus
      } = options

      // Build queries
      const queries = [Query.equal('userId', userId)]

      // Add filters
      if (search) {
        queries.push(Query.or([
          Query.search('productName', search),
          Query.search('orderNumber', search)
        ]))
      }

      if (storeCodeId) {
        queries.push(Query.equal('storeCodeId', storeCodeId))
      }

      if (customerId) {
        queries.push(Query.equal('customerId', customerId))
      }

      if (isBought !== undefined) {
        queries.push(Query.equal('isBought', isBought))
      }

      if (packingStatus) {
        queries.push(Query.equal('packingStatus', packingStatus))
      }

      // Add sorting
      const appwriteSortField = sortField === 'createdAt' ? '$createdAt' : sortField
      if (sortDirection === 'asc') {
        queries.push(Query.orderAsc(appwriteSortField))
      } else {
        queries.push(Query.orderDesc(appwriteSortField))
      }

      // Add pagination
      const offset = (page - 1) * limit
      queries.push(Query.limit(limit))
      queries.push(Query.offset(offset))

      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        queries
      )

      const orders = response.documents.map(doc => convertFromAppwriteDocument<Order>(doc))

      return {
        data: orders,
        pagination: {
          page,
          limit,
          total: response.total,
          totalPages: Math.ceil(response.total / limit)
        }
      }
    } catch (error: any) {
      throw new Error(`Failed to fetch orders: ${error.message}`)
    }
  }

  // Get order by ID
  async getOrderById(request: NextRequest, id: string): Promise<Order | null> {
    try {
      const userId = await getUserIdFromRequest(request)
      if (!userId) {
        throw new Error('User not authenticated')
      }

      const userClient = this.createUserClient(request)
      const databases = new Databases(userClient)

      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id
      )

      const order = convertFromAppwriteDocument<Order>(response)
      
      // Verify ownership
      if (order.userId !== userId) {
        throw new Error('Access denied')
      }

      return order
    } catch (error: any) {
      if (error.code === 404) {
        return null
      }
      throw new Error(`Failed to fetch order: ${error.message}`)
    }
  }

  // Create new order
  async createOrder(request: NextRequest, data: CreateOrderData): Promise<Order> {
    try {
      const userId = await getUserIdFromRequest(request)
      if (!userId) {
        throw new Error('User not authenticated')
      }

      const userClient = this.createUserClient(request)
      const databases = new Databases(userClient)

      const documentData = {
        ...convertToAppwriteDocument(data),
        quantity: data.quantity ?? 1,
        storePrice: data.storePrice ?? 0.00,
        pasabuyFee: data.pasabuyFee ?? 0.00,
        customerPrice: data.customerPrice ?? 0.00,
        isBought: data.isBought ?? false,
        packingStatus: data.packingStatus || 'Not Packed',
        priority: data.priority || 'NORMAL',
        condition: data.condition || 'NEW',
        urgency: data.urgency || 'MEDIUM',
        deliveryStatus: data.deliveryStatus || 'Not Delivered',
        userId: userId
      }

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        ID.unique(),
        documentData,
        [
          Permission.read(Role.user(userId)),
          Permission.write(Role.user(userId))
        ]
      )

      return convertFromAppwriteDocument<Order>(response)
    } catch (error: any) {
      throw new Error(`Failed to create order: ${error.message}`)
    }
  }

  // Update order
  async updateOrder(request: NextRequest, id: string, data: UpdateOrderData): Promise<Order> {
    try {
      const userId = await getUserIdFromRequest(request)
      if (!userId) {
        throw new Error('User not authenticated')
      }

      const userClient = this.createUserClient(request)
      const databases = new Databases(userClient)

      // Verify ownership
      const existing = await this.getOrderById(request, id)
      if (!existing) {
        throw new Error('Order not found')
      }

      const updateData = convertToAppwriteDocument(data)

      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id,
        updateData
      )

      return convertFromAppwriteDocument<Order>(response)
    } catch (error: any) {
      throw new Error(`Failed to update order: ${error.message}`)
    }
  }

  // Delete order
  async deleteOrder(request: NextRequest, id: string): Promise<void> {
    try {
      const userId = await getUserIdFromRequest(request)
      if (!userId) {
        throw new Error('User not authenticated')
      }

      const userClient = this.createUserClient(request)
      const databases = new Databases(userClient)

      // Verify ownership
      const existing = await this.getOrderById(request, id)
      if (!existing) {
        throw new Error('Order not found')
      }

      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.ORDERS,
        id
      )
    } catch (error: any) {
      throw new Error(`Failed to delete order: ${error.message}`)
    }
  }

  // Mark order as bought
  async markAsBought(request: NextRequest, id: string, storePrice: number, pasabuyFee: number, customerPrice: number): Promise<Order> {
    return this.updateOrder(request, id, {
      isBought: true,
      storePrice,
      pasabuyFee,
      customerPrice
    })
  }

  // Mark order as packed
  async markAsPacked(request: NextRequest, id: string): Promise<Order> {
    return this.updateOrder(request, id, {
      packingStatus: 'Packed'
    })
  }
}

// Create and export server service instance
export const appwriteOrderServiceServer = new AppwriteOrderServiceServer()
