/**
 * Appwrite health check endpoint
 * Provides system health monitoring for production deployment
 */

import { NextRequest, NextResponse } from 'next/server'
import { serverDatabases, serverStorage, DB_ID, STORAGE_BUCKET_ID } from '@/lib/appwrite-server'
import { handleError, safeAsync } from '@/lib/utils/error-handler'

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  checks: {
    database: {
      status: 'healthy' | 'degraded' | 'unhealthy'
      responseTime?: number
      error?: string
    }
    storage: {
      status: 'healthy' | 'degraded' | 'unhealthy'
      responseTime?: number
      error?: string
    }
    environment: {
      status: 'healthy' | 'degraded' | 'unhealthy'
      missingVars?: string[]
    }
  }
  version: string
  uptime: number
}

/**
 * Check database connectivity
 */
async function checkDatabase(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy'
  responseTime?: number
  error?: string
}> {
  const startTime = performance.now()
  
  const { data, error } = await safeAsync(async () => {
    return await serverDatabases.get(DB_ID)
  }, { action: 'health_check_database' })
  
  const responseTime = performance.now() - startTime
  
  if (error) {
    return {
      status: 'unhealthy',
      responseTime,
      error: error.userMessage
    }
  }
  
  return {
    status: responseTime > 5000 ? 'degraded' : 'healthy',
    responseTime
  }
}

/**
 * Check storage connectivity
 */
async function checkStorage(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy'
  responseTime?: number
  error?: string
}> {
  const startTime = performance.now()
  
  const { data, error } = await safeAsync(async () => {
    return await serverStorage.getBucket(STORAGE_BUCKET_ID)
  }, { action: 'health_check_storage' })
  
  const responseTime = performance.now() - startTime
  
  if (error) {
    return {
      status: 'unhealthy',
      responseTime,
      error: error.userMessage
    }
  }
  
  return {
    status: responseTime > 5000 ? 'degraded' : 'healthy',
    responseTime
  }
}

/**
 * Check environment configuration
 */
function checkEnvironment(): {
  status: 'healthy' | 'degraded' | 'unhealthy'
  missingVars?: string[]
} {
  const requiredVars = [
    'NEXT_PUBLIC_APPWRITE_ENDPOINT',
    'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
    'APPWRITE_API_KEY',
    'APPWRITE_DATABASE_ID',
    'APPWRITE_STORAGE_BUCKET_ID'
  ]
  
  const missingVars = requiredVars.filter(varName => !process.env[varName])
  
  if (missingVars.length === 0) {
    return { status: 'healthy' }
  }
  
  return {
    status: missingVars.length > 2 ? 'unhealthy' : 'degraded',
    missingVars
  }
}

/**
 * GET /api/health/appwrite
 * Returns comprehensive health check results
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const startTime = Date.now()
    
    // Run all health checks in parallel
    const [databaseCheck, storageCheck, environmentCheck] = await Promise.all([
      checkDatabase(),
      checkStorage(),
      checkEnvironment()
    ])
    
    const endTime = Date.now()
    
    // Determine overall status
    const checks = {
      database: databaseCheck,
      storage: storageCheck,
      environment: environmentCheck
    }
    
    const statuses = Object.values(checks).map(check => check.status)
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy'
    
    if (statuses.includes('unhealthy')) {
      overallStatus = 'unhealthy'
    } else if (statuses.includes('degraded')) {
      overallStatus = 'degraded'
    } else {
      overallStatus = 'healthy'
    }
    
    const result: HealthCheckResult = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks,
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime()
    }
    
    // Set appropriate HTTP status code
    const httpStatus = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503
    
    return NextResponse.json(result, { 
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    
  } catch (error) {
    const handledError = handleError(error, { action: 'health_check' })
    
    const result: HealthCheckResult = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: { status: 'unhealthy', error: 'Health check failed' },
        storage: { status: 'unhealthy', error: 'Health check failed' },
        environment: { status: 'unhealthy' }
      },
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime()
    }
    
    return NextResponse.json(result, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}

/**
 * HEAD /api/health/appwrite
 * Simple health check for load balancers
 */
export async function HEAD(request: NextRequest): Promise<NextResponse> {
  try {
    // Quick database connectivity check
    await serverDatabases.get(DB_ID)
    
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })
  } catch (error) {
    return new NextResponse(null, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })
  }
}
