import { NextRequest } from 'next/server'
import { appwriteStoreService } from '@/lib/services/appwrite-store-service'
import { getUserIdFromRequest } from '@/lib/auth-server'
import { withApiMiddleware, ApiContext } from '@/lib/middleware/api-middleware'
import { InputValidator } from '@/lib/utils/security'

async function handleGetStoreCodes(request: NextRequest, context: ApiContext) {
  // Check authentication
  const userId = await getUserIdFromRequest(request)
  if (!userId) {
    throw new Error('Authentication required')
  }

  const { searchParams } = new URL(request.url)
  const search = searchParams.get('search')

  let storeCodes
  if (search) {
    storeCodes = await appwriteStoreService.searchStoreCodes(search, userId)
  } else {
    storeCodes = await appwriteStoreService.getStoreCodesWithCounts(userId)
  }

  return storeCodes
}

export const GET = withApiMiddleware(handleGetStoreCodes, {
  requireAuth: true,
  rateLimit: { maxRequests: 100, windowMs: 900000 }, // 100 requests per 15 minutes
  validateMethod: ['GET']
})

async function handleCreateStoreCode(request: NextRequest, context: ApiContext) {
  // Check authentication
  const userId = await getUserIdFromRequest(request)
  if (!userId) {
    throw new Error('Authentication required')
  }

  const body = await request.json()
  const { code, name } = body

  // Validate input
  const codeError = InputValidator.required(code, 'Store code') ||
                   InputValidator.storeCode(code)
  if (codeError) {
    throw new Error(codeError)
  }

  const nameError = name ? InputValidator.maxLength(name, 100, 'Store name') : null
  if (nameError) {
    throw new Error(nameError)
  }

  const storeCode = await appwriteStoreService.createStoreCode({
    code: code.trim().toUpperCase(),
    name: name?.trim() || null
  }, userId)

  return storeCode
}

export const POST = withApiMiddleware(handleCreateStoreCode, {
  requireAuth: true,
  rateLimit: { maxRequests: 50, windowMs: 900000 }, // 50 requests per 15 minutes
  validateMethod: ['POST']
})
