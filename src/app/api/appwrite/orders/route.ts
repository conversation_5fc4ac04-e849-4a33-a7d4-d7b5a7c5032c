import { NextRequest, NextResponse } from 'next/server'
import { serverDatabases, DB_ID, ORDERS_COLLECTION_ID, Query, convertFromAppwriteDocument } from '@/lib/appwrite-server'
import { OrderQueryOptions, Order } from '@/lib/services/appwrite-order-service'
import { getUserIdFromRequest } from '@/lib/auth-server'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const userId = await getUserIdFromRequest(request)
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)

    // Extract pagination parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '15')

    // Extract sorting parameters
    const sortParam = searchParams.get('sort') // format: "field:direction"
    let sortField = 'createdAt'
    let sortDirection: 'asc' | 'desc' = 'desc'

    if (sortParam) {
      const [field, direction] = sortParam.split(':')
      if (field) sortField = field
      if (direction === 'asc' || direction === 'desc') sortDirection = direction
    }

    // Extract filter parameters
    const search = searchParams.get('search')
    const storeCodeId = searchParams.get('storeCodeId')
    const customerId = searchParams.get('customerId')
    const isBought = searchParams.get('isBought')
    const packingStatus = searchParams.get('packingStatus')
    const type = searchParams.get('type') // 'buy-list' or 'packing-list'

    // Build query options
    const options: OrderQueryOptions = {
      page,
      limit,
      sortField,
      sortDirection,
      search: search || undefined,
      storeCodeId: storeCodeId || undefined,
      customerId: customerId || undefined,
      isBought: isBought ? isBought === 'true' : undefined,
      packingStatus: packingStatus || undefined
    }

    let result

    // Build queries using server-side client with API key
    const queries = [Query.equal('userId', userId)]

    // Add filters
    if (search) {
      queries.push(Query.or([
        Query.search('productName', search),
        Query.search('orderNumber', search)
      ]))
    }

    if (storeCodeId) {
      queries.push(Query.equal('storeCodeId', storeCodeId))
    }

    if (customerId) {
      queries.push(Query.equal('customerId', customerId))
    }

    // Handle special types
    if (type === 'buy-list') {
      queries.push(Query.equal('isBought', false))
    } else if (type === 'packing-list') {
      queries.push(Query.equal('isBought', true))
      queries.push(Query.equal('packingStatus', 'Not Packed'))
    } else {
      // Apply regular filters
      if (isBought !== undefined && isBought !== null) {
        queries.push(Query.equal('isBought', isBought === 'true'))
      }

      if (packingStatus) {
        queries.push(Query.equal('packingStatus', packingStatus))
      }
    }

    // Add sorting
    const appwriteSortField = sortField === 'createdAt' ? '$createdAt' : sortField
    if (sortDirection === 'asc') {
      queries.push(Query.orderAsc(appwriteSortField))
    } else {
      queries.push(Query.orderDesc(appwriteSortField))
    }

    // Add pagination
    const offset = (page - 1) * limit
    queries.push(Query.limit(limit))
    queries.push(Query.offset(offset))

    // Execute query using server-side client
    const response = await serverDatabases.listDocuments(
      DB_ID,
      ORDERS_COLLECTION_ID,
      queries
    )

    const orders = response.documents.map(doc => convertFromAppwriteDocument<Order>(doc))

    result = {
      data: orders,
      pagination: {
        page,
        limit,
        total: response.total,
        totalPages: Math.ceil(response.total / limit)
      }
    }

    return NextResponse.json(result)
  } catch (error: unknown) {
    console.error('Error fetching orders:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userId = await getUserIdFromRequest(request)
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { productName } = body

    if (!productName) {
      return NextResponse.json(
        { error: 'Product name is required' },
        { status: 400 }
      )
    }

    // For now, let's use a simple approach for creating orders
    // We'll implement this properly later
    throw new Error('Order creation not implemented in this version')
  } catch (error: unknown) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error creating order' },
      { status: 500 }
    )
  }
}
